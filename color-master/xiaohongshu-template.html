<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书软件工具分享模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            padding: 20px;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 15px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.8em;
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .attract { background: linear-gradient(45deg, #ff6b6b, #ff8e8e); }
        .info { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .features { background: linear-gradient(45deg, #feca57, #ff9ff3); }
        .tutorial { background: linear-gradient(45deg, #1dd1a1, #55efc4); }
        .experience { background: linear-gradient(45deg, #fd79a8, #fdcb6e); }
        .audience { background: linear-gradient(45deg, #6c5ce7, #a29bfe); }
        .download { background: linear-gradient(45deg, #00b894, #00cec9); }
        .interaction { background: linear-gradient(45deg, #e17055, #fab1a0); }
        
        .content {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-top: 10px;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }
        
        .feature-item, .step-item, .point-item {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            border-left: 4px solid #ff6b6b;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover, .step-item:hover, .point-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .feature-number, .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .checkmark {
            color: #00b894;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .crossmark {
            color: #e17055;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            transition: transform 0.3s ease;
        }
        
        .tag:hover {
            transform: scale(1.1);
        }
        
        .input-field {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1em;
            margin: 8px 0;
            transition: border-color 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }
        
        .placeholder {
            color: #999;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section-title {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 小红书软件工具分享模板</h1>
            <p>让每个软件分享都成为爆款笔记！</p>
        </div>

        <!-- 开头吸引部分 -->
        <div class="section">
            <div class="section-title attract">🎯 开头吸引</div>
            <div class="content">
                <p><span class="emoji">🔥</span>又发现一个宝藏软件！<input type="text" class="input-field" placeholder="想要自由上网访问ChatGPT" value="想要自由上网访问ChatGPT">的姐妹们有救了！</p>
                <p><span class="emoji">✨</span>这个<input type="text" class="input-field" placeholder="VPN工具" value="VPN工具">真的太好用了，<input type="text" class="input-field" placeholder="让我轻松访问全球网站，ChatGPT随便用！" value="让我轻松访问全球网站，ChatGPT随便用！">！</p>
            </div>
        </div>

        <!-- 软件基本信息 -->
        <div class="section">
            <div class="section-title info">📱 软件基本信息</div>
            <div class="content">
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>软件名称：</strong><input type="text" class="input-field" placeholder="VPN代理工具" value="VPN代理工具">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>适用系统：</strong><input type="text" class="input-field" placeholder="Windows/Mac/iOS/Android" value="Windows/Mac/iOS/Android">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>收费情况：</strong><input type="text" class="input-field" placeholder="付费订阅（新用户优惠5元）" value="付费订阅（新用户优惠5元）">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>软件大小：</strong><input type="text" class="input-field" placeholder="约30-50MB" value="约30-50MB">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>推荐指数：</strong><span class="highlight">⭐⭐⭐⭐⭐</span>
                </div>
            </div>
        </div>

        <!-- 核心功能介绍 -->
        <div class="section">
            <div class="section-title features">💡 核心功能介绍</div>
            <div class="content">
                <div class="feature-item">
                    <span class="feature-number">1</span>
                    <strong><input type="text" class="input-field" placeholder="全球节点覆盖" value="全球节点覆盖"></strong>
                    <div style="margin-left: 40px; margin-top: 10px;">
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="支持香港、新加坡、日本、台湾等多个地区" value="支持香港、新加坡、日本、台湾等多个地区"></div>
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="连接稳定，速度超快，看视频无卡顿" value="连接稳定，速度超快，看视频无卡顿"></div>
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-number">2</span>
                    <strong><input type="text" class="input-field" placeholder="一键订阅配置" value="一键订阅配置"></strong>
                    <div style="margin-left: 40px; margin-top: 10px;">
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="复制订阅链接即可使用，小白也能轻松上手" value="复制订阅链接即可使用，小白也能轻松上手"></div>
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="支持多设备同时使用，手机电脑都能用" value="支持多设备同时使用，手机电脑都能用"></div>
                    </div>
                </div>

                <div class="feature-item">
                    <span class="feature-number">3</span>
                    <strong><input type="text" class="input-field" placeholder="ChatGPT专用优化" value="ChatGPT专用优化"></strong>
                    <div style="margin-left: 40px; margin-top: 10px;">
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="专门优化ChatGPT访问，登录无障碍" value="专门优化ChatGPT访问，登录无障碍"></div>
                        <div><span class="checkmark">✅</span><input type="text" class="input-field" placeholder="解决登录异常问题，配合无痕模式使用更稳定" value="解决登录异常问题，配合无痕模式使用更稳定"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用教程/技巧 -->
        <div class="section">
            <div class="section-title tutorial">🎨 使用教程/技巧</div>
            <div class="content">
                <div class="step-item">
                    <span class="emoji">📌</span><strong>第一步：</strong><input type="text" class="input-field" placeholder="注册购买流量套餐" value="注册购买流量套餐">
                </div>
                <div class="step-item">
                    <span class="emoji">📌</span><strong>第二步：</strong><input type="text" class="input-field" placeholder="下载对应系统的VPN客户端软件" value="下载对应系统的VPN客户端软件">
                </div>
                <div class="step-item">
                    <span class="emoji">📌</span><strong>第三步：</strong><input type="text" class="input-field" placeholder="复制订阅地址到软件中，选择节点开启代理" value="复制订阅地址到软件中，选择节点开启代理">
                </div>
                <div class="step-item" style="background: rgba(255, 206, 84, 0.2); border-left-color: #feca57;">
                    <span class="emoji">💡</span><strong>小贴士：</strong><input type="text" class="input-field" placeholder="使用ChatGPT时选择新加坡、日本、台湾节点最稳定！" value="使用ChatGPT时选择新加坡、日本、台湾节点最稳定！">
                </div>
            </div>
        </div>

        <!-- 实际体验分享 -->
        <div class="section">
            <div class="section-title experience">🔥 实际体验分享</div>
            <div class="content">
                <p><span class="emoji">⏰</span>用了<input type="text" class="input-field" placeholder="半个月" value="半个月">，真的爱了！</p>

                <div class="point-item" style="border-left-color: #00b894;">
                    <span class="checkmark">✅</span><strong>优点：</strong>
                    <div style="margin-left: 20px; margin-top: 10px;">
                        <div>• <input type="text" class="input-field" placeholder="连接速度快，看YouTube 4K无压力" value="连接速度快，看YouTube 4K无压力"></div>
                        <div>• <input type="text" class="input-field" placeholder="ChatGPT访问稳定，再也不用担心登录失败" value="ChatGPT访问稳定，再也不用担心登录失败"></div>
                        <div>• <input type="text" class="input-field" placeholder="操作简单，小白5分钟就能上手" value="操作简单，小白5分钟就能上手"></div>
                    </div>
                </div>

                <div class="point-item" style="border-left-color: #e17055;">
                    <span class="crossmark">❌</span><strong>缺点：</strong>
                    <div style="margin-left: 20px; margin-top: 10px;">
                        <div>• <input type="text" class="input-field" placeholder="需要付费，不过价格还算合理" value="需要付费，不过价格还算合理"></div>
                        <div>• <input type="text" class="input-field" placeholder="偶尔需要切换节点找最优线路" value="偶尔需要切换节点找最优线路"></div>
                    </div>
                </div>

                <div class="point-item" style="border-left-color: #fdcb6e;">
                    <span class="emoji">💰</span><strong>性价比：</strong><input type="text" class="input-field" placeholder="超高！新用户优惠后性价比无敌" value="超高！新用户优惠后性价比无敌">
                </div>
            </div>
        </div>

        <!-- 适合人群 -->
        <div class="section">
            <div class="section-title audience">👥 适合人群</div>
            <div class="content">
                <div class="point-item">
                    <span class="emoji">✨</span><strong>学生党：</strong><input type="text" class="input-field" placeholder="查资料、写论文必备，访问国外学术网站" value="查资料、写论文必备，访问国外学术网站">
                </div>
                <div class="point-item">
                    <span class="emoji">✨</span><strong>上班族：</strong><input type="text" class="input-field" placeholder="工作需要访问海外网站，ChatGPT提高工作效率" value="工作需要访问海外网站，ChatGPT提高工作效率">
                </div>
                <div class="point-item">
                    <span class="emoji">✨</span><strong>自媒体人：</strong><input type="text" class="input-field" placeholder="获取海外资讯，ChatGPT辅助内容创作" value="获取海外资讯，ChatGPT辅助内容创作">
                </div>
                <div class="point-item">
                    <span class="emoji">✨</span><strong><input type="text" class="input-field" placeholder="程序员" value="程序员">：</strong><input type="text" class="input-field" placeholder="访问GitHub、Stack Overflow等开发网站" value="访问GitHub、Stack Overflow等开发网站">
                </div>
            </div>
        </div>

        <!-- 获取方式 -->
        <div class="section">
            <div class="section-title download">📥 获取方式</div>
            <div class="content">
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>官方下载：</strong><input type="text" class="input-field" placeholder="根据系统下载对应客户端" value="根据系统下载对应客户端">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>官网地址：</strong><input type="text" class="input-field" placeholder="efcloud.pages.dev（多个备用域名）" value="efcloud.pages.dev（多个备用域名）">
                </div>
                <div class="point-item">
                    <span class="emoji">🔸</span><strong>注意事项：</strong><input type="text" class="input-field" placeholder="新用户注册输入new可享受5元优惠" value="新用户注册输入new可享受5元优惠">
                </div>
            </div>
        </div>

        <!-- 互动引导 -->
        <div class="section">
            <div class="section-title interaction">💬 互动引导</div>
            <div class="content">
                <p><span class="emoji">🤔</span>你们还在用什么好用的<input type="text" class="input-field" placeholder="VPN工具" value="VPN工具">？</p>
                <p><span class="emoji">💭</span>评论区分享一下，一起变更高效！🤝</p>

                <div class="tags">
                    <span class="tag">#软件推荐</span>
                    <span class="tag">#效率工具</span>
                    <span class="tag">#VPN工具</span>
                    <span class="tag">#ChatGPT</span>
                    <span class="tag">#干货分享</span>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="section" style="background: rgba(255, 255, 255, 0.1); border: 2px dashed #ff6b6b;">
            <div class="section-title" style="background: linear-gradient(45deg, #74b9ff, #0984e3); text-align: center;">
                📝 使用说明
            </div>
            <div class="content">
                <p><span class="emoji">💡</span><strong>如何使用这个模板：</strong></p>
                <ol style="margin-left: 20px; margin-top: 10px;">
                    <li>在每个输入框中填入对应的软件信息</li>
                    <li>根据软件特点调整内容的详细程度</li>
                    <li>复制填写完成的内容到小红书发布</li>
                    <li>记得配上精美的软件截图和使用效果图</li>
                </ol>
                <p style="margin-top: 15px;"><span class="emoji">🎯</span><strong>小贴士：</strong>保持内容真实性，分享真实使用感受更容易获得用户信任！</p>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 输入框焦点效果
            const inputs = document.querySelectorAll('.input-field');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.transform = 'scale(1.02)';
                });

                input.addEventListener('blur', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 标签点击效果
            const tags = document.querySelectorAll('.tag');
            tags.forEach(tag => {
                tag.addEventListener('click', function() {
                    this.style.background = 'linear-gradient(45deg, #00b894, #00cec9)';
                    setTimeout(() => {
                        this.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                    }, 300);
                });
            });
        });
    </script>
</body>
</html>
