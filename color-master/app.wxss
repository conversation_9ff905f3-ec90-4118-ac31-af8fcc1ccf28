/**app.wxss**/
/* 全局样式重置 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
}

/* 通用容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn {
  width: 80%;
  margin: 20rpx 0;
  background-color: #007aff;
  color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn:active {
  background-color: #0056b3;
  transform: scale(0.98);
}

/* 通用颜色显示 */
.color-display {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  border: 2rpx solid #e9ecef;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 通用滑块容器 */
.slider-container {
  width: 90%;
  margin: 20rpx 0;
}

.slider-label {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #1a1a1a;
}

.slider-value {
  color: #666;
  font-weight: 600;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

/* 通用文本样式 */
.text-primary {
  color: #1a1a1a;
}

.text-secondary {
  color: #8e8e93;
}

.text-muted {
  color: #c7c7cc;
}

/* 通用间距 */
.mb-small {
  margin-bottom: 16rpx;
}

.mb-medium {
  margin-bottom: 32rpx;
}

.mb-large {
  margin-bottom: 48rpx;
}
