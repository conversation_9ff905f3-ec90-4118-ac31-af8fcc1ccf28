# 像素级颜色选择功能演示

## 功能特点

### 🔍 像素放大镜
- 滑动时显示当前位置周围的像素网格
- 9x9像素网格，清晰显示每个像素的颜色
- 中心像素有特殊标记，便于精确选择

### 🎯 直接在原图操作
- 无需弹框遮挡，直接在原图上滑动选择
- 像素放大镜跟随手指移动
- 不影响下方颜色信息的查看

### 🌈 实时颜色预览
- 滑动过程中颜色信息实时更新
- RGB值和百分比同步显示
- 颜色预览块实时变化

### ⚡ 性能优化
- 30ms防抖处理，更快响应
- Canvas复用提升响应速度
- 智能的像素数据提取算法

## 核心代码实现

### 1. 原图触摸事件处理

```javascript
// 图片触摸移动
onImageTouchMove(e) {
  if (!this.data.isSelecting) return

  const touch = e.touches[0]
  this.updateColorSelection(touch.x, touch.y)
}

// 更新颜色选择
updateColorSelection(x, y) {
  wx.createSelectorQuery()
    .select('.selected-image')
    .boundingClientRect((rect) => {
      if (!rect) return

      const relativeX = x - rect.left
      const relativeY = y - rect.top

      // 映射到原图坐标比例
      const xRatio = relativeX / rect.width
      const yRatio = relativeY / rect.height

      // 显示像素放大镜
      this.setData({
        showPixelMagnifier: true,
        pixelMagnifierX: x,
        pixelMagnifierY: y
      })

      this.extractColorAndPixels(originalX, originalY)
    })
    .exec()
}
```

### 2. 像素数据提取

```javascript
// 提取周围像素
extractSurroundingPixels(centerX, centerY) {
  const magnifierSize = 9 // 9x9像素网格
  const halfSize = Math.floor(magnifierSize / 2)

  wx.canvasGetImageData({
    canvasId: 'hiddenCanvas',
    x: startX,
    y: startY,
    width: actualWidth,
    height: actualHeight,
    success(res) {
      const pixelData = []
      const data = res.data

      // 转换像素数据为颜色数组
      for (let i = 0; i < data.length; i += 4) {
        pixelData.push({
          r: data[i],
          g: data[i + 1],
          b: data[i + 2]
        })
      }

      that.setData({
        pixelMagnifierData: {
          pixels: pixelData,
          width: actualWidth,
          height: actualHeight,
          centerX: centerX - startX,
          centerY: centerY - startY
        }
      })
    }
  }, this)
}
```

### 3. 像素放大镜界面

```xml
<!-- 像素放大镜 -->
<view class="pixel-magnifier" wx:if="{{showPixelMagnifier && pixelMagnifierData}}"
      style="left: {{pixelMagnifierX - 60}}px; top: {{pixelMagnifierY - 60}}px;">
  <view class="pixel-grid">
    <view class="pixel-row" wx:for="{{pixelMagnifierData.height}}" wx:for-index="row" wx:key="row">
      <view class="pixel-cell"
            wx:for="{{pixelMagnifierData.width}}"
            wx:for-index="col"
            wx:key="col"
            style="background-color: rgb({{pixelMagnifierData.pixels[row * pixelMagnifierData.width + col].r}}, {{pixelMagnifierData.pixels[row * pixelMagnifierData.width + col].g}}, {{pixelMagnifierData.pixels[row * pixelMagnifierData.width + col].b}});"
            class="{{row === pixelMagnifierData.centerY && col === pixelMagnifierData.centerX ? 'center-pixel' : ''}}">
      </view>
    </view>
  </view>
</view>
```

### 4. 像素放大镜样式

```css
.pixel-magnifier {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  background: white;
  border: 4rpx solid #007aff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  z-index: 100;
  pointer-events: none;
}

.center-pixel {
  border: 2rpx solid #007aff !important;
  box-shadow: inset 0 0 8rpx rgba(0, 122, 255, 0.5);
}
```

## 交互流程

1. **启动**: 用户点击图片，进入颜色选择模式
2. **提示**: 显示"滑动选择颜色"提示，图片上的引导文字消失
3. **选择**: 用户在原图上滑动手指
4. **反馈**: 像素放大镜跟随手指，显示周围像素网格，颜色信息实时更新
5. **确认**: 用户松开手指，确认颜色选择
6. **完成**: 像素放大镜消失，显示选择的颜色和选择点标记

## 技术亮点

### 坐标映射算法
- 精确计算触摸坐标到图片像素的映射
- 支持不同尺寸图片的自适应处理
- 边界检查确保坐标有效性

### 性能优化策略
- 50ms防抖处理减少不必要的计算
- Canvas预准备和复用机制
- 智能的错误处理和降级方案

### 用户体验设计
- 流畅的动画过渡效果
- 清晰的视觉反馈和状态提示
- 直观的操作引导和帮助信息

## 兼容性说明

- 支持微信小程序基础库 2.19.4+
- 兼容不同屏幕尺寸和分辨率
- 提供备用颜色提取方案确保功能可用性

## 使用建议

1. **图片选择**: 建议使用颜色丰富、对比度高的图片
2. **操作方式**: 缓慢滑动可获得更精确的颜色选择
3. **性能考虑**: 避免过快连续滑动以确保最佳体验
