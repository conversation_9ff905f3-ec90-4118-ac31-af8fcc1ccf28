# 颜色大师 - 微信小程序

一款专业的颜色分析与调整微信小程序，支持拍照提取颜色、分析三原色比例、实时调整RGB值。

## 功能特性

### 🎨 核心功能
- **拍照取色**：支持拍照或从相册选择图片
- **精准取色**：点击图片任意位置提取颜色
- **颜色分析**：自动计算RGB值和三原色比例
- **实时调整**：通过滑块实时调整RGB值
- **颜色预览**：原始颜色与调整后颜色对比显示

### 📱 用户体验
- **直观界面**：现代化渐变设计，操作简单直观
- **实时反馈**：滑块调整时颜色实时变化
- **多格式显示**：支持RGB、十六进制、百分比显示
- **颜色历史**：自动保存调整过的颜色
- **一键复制**：快速复制十六进制颜色值

### 🔧 技术特性
- **Canvas取色**：使用Canvas API精确提取像素颜色
- **本地存储**：颜色历史本地保存，支持离线查看
- **分享功能**：支持分享颜色到微信好友
- **响应式设计**：适配不同尺寸的手机屏幕

## 页面结构

```
pages/
├── index/              # 首页 - 拍照取色
│   ├── index.wxml     # 页面结构
│   ├── index.wxss     # 页面样式
│   ├── index.js       # 页面逻辑
│   └── index.json     # 页面配置
├── colorAnalysis/      # 颜色调整页
│   ├── colorAnalysis.wxml
│   ├── colorAnalysis.wxss
│   ├── colorAnalysis.js
│   └── colorAnalysis.json
└── colorHistory/       # 颜色历史页
    ├── colorHistory.wxml
    ├── colorHistory.wxss
    ├── colorHistory.js
    └── colorHistory.json
```

## 技术实现

### 颜色提取算法
```javascript
// 使用Canvas API获取像素数据
wx.canvasGetImageData({
  canvasId: 'hiddenCanvas',
  x: imageX,
  y: imageY,
  width: 1,
  height: 1,
  success(res) {
    const data = res.data
    const r = data[0]  // 红色值
    const g = data[1]  // 绿色值
    const b = data[2]  // 蓝色值
  }
})
```

### 三原色比例计算
```javascript
const total = r + g + b
const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
const bPercent = total > 0 ? Math.round((b / total) * 100) : 0
```

### RGB转十六进制
```javascript
const rgbToHex = (r, g, b) => {
  const toHex = (n) => {
    const hex = n.toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase()
}
```

## 使用说明

### 基本使用流程
1. **选择图片**：点击"选择图片"按钮，选择拍照或从相册选择
2. **提取颜色**：点击图片上的任意位置选择要分析的颜色
3. **查看分析**：查看颜色的RGB值和三原色比例
4. **调整颜色**：点击"进入颜色调整"进行实时调整
5. **保存颜色**：调整完成后保存颜色到历史记录

### 高级功能
- **颜色历史**：查看和管理所有保存过的颜色
- **颜色复制**：一键复制十六进制颜色值到剪贴板
- **颜色分享**：将调整好的颜色分享给微信好友

## 开发环境

- **微信开发者工具**：最新版本
- **基础库版本**：2.19.4 或以上
- **支持平台**：微信小程序

## 部署说明

1. 使用微信开发者工具打开项目
2. 配置小程序AppID（在project.config.json中）
3. 上传代码到微信小程序后台
4. 提交审核并发布

## 权限说明

- **相机权限**：用于拍照获取颜色信息
- **相册权限**：用于从相册选择图片
- **剪贴板权限**：用于复制颜色值

## 技术支持

如有问题或建议，请联系开发团队。

---

**颜色大师** - 让颜色分析变得简单有趣！
