// pages/colorHistory/colorHistory.js
Page({
  data: {
    colorList: []
  },

  onLoad() {
    this.loadColorHistory()
  },

  onShow() {
    // 每次显示页面时重新加载数据
    this.loadColorHistory()
  },

  // 加载颜色历史
  loadColorHistory() {
    const savedColors = wx.getStorageSync('savedColors') || []
    
    // 格式化时间显示
    const colorList = savedColors.map(color => ({
      ...color,
      timeText: this.formatTime(color.timestamp)
    }))
    
    this.setData({
      colorList: colorList
    })
    
    console.log('加载颜色历史:', colorList.length, '个颜色')
  },

  // 格式化时间
  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 2592000000) { // 30天内
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      const date = new Date(timestamp)
      return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`
    }
  },

  // 选择颜色（点击整个item）
  selectColor(e) {
    const color = e.currentTarget.dataset.color
    wx.navigateTo({
      url: `/pages/colorAnalysis/colorAnalysis?r=${color.r}&g=${color.g}&b=${color.b}`
    })
  },

  // 复制颜色值
  copyColor(e) {
    const hex = e.currentTarget.dataset.hex
    wx.setClipboardData({
      data: hex,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        })
      }
    })
  },

  // 编辑颜色
  editColor(e) {
    const color = e.currentTarget.dataset.color
    wx.navigateTo({
      url: `/pages/colorAnalysis/colorAnalysis?r=${color.r}&g=${color.g}&b=${color.b}`
    })
  },

  // 删除单个颜色
  deleteColor(e) {
    const index = e.currentTarget.dataset.index
    const that = this
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个颜色吗？',
      success(res) {
        if (res.confirm) {
          let savedColors = wx.getStorageSync('savedColors') || []
          savedColors.splice(index, 1)
          wx.setStorageSync('savedColors', savedColors)
          
          that.loadColorHistory()
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 清空所有历史
  clearAll() {
    const that = this
    
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有颜色历史吗？此操作不可恢复。',
      success(res) {
        if (res.confirm) {
          wx.removeStorageSync('savedColors')
          that.setData({
            colorList: []
          })
          
          wx.showToast({
            title: '清空成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 返回首页
  goToIndex() {
    wx.navigateBack({
      delta: 1
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '颜色大师 - 拍照分析颜色，调整三原色比例',
      path: '/pages/index/index'
    }
  }
})
