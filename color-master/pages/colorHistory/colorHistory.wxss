/* pages/colorHistory/colorHistory.wxss */
.page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.nav-bar {
  background: white;
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 48rpx;
  color: #007aff;
  font-weight: 300;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.nav-action {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-text {
  font-size: 32rpx;
  color: #ff3b30;
}

.nav-placeholder {
  width: 60rpx;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 40rpx;
}

.color-list {
  margin-bottom: 40rpx;
}

.color-item {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  gap: 32rpx;
  transition: all 0.3s ease;
}

.color-item:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.color-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.color-details {
  flex: 1;
}

.rgb-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  letter-spacing: 2rpx;
}

.hex-text {
  display: block;
  font-size: 28rpx;
  color: #8e8e93;
  font-family: 'SF Mono', 'Monaco', 'Courier New', monospace;
  margin-bottom: 12rpx;
}

.save-time {
  font-size: 24rpx;
  color: #c7c7cc;
}

.color-actions {
  display: flex;
  gap: 16rpx;
  flex-shrink: 0;
}

.action-btn {
  width: 72rpx;
  height: 72rpx;
  background: #f2f2f7;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
  opacity: 0.6;
}

.action-icon {
  font-size: 32rpx;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #8e8e93;
  margin-bottom: 48rpx;
}

.start-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}
