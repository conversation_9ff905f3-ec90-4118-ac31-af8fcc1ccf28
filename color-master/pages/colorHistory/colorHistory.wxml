<!--pages/colorHistory/colorHistory.wxml-->
<view class="page">
  <!-- 顶部导航 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="goToIndex">
      <text class="back-icon">‹</text>
    </view>
    <text class="nav-title">颜色历史</text>
    <view class="nav-action" wx:if="{{colorList.length > 0}}" bindtap="clearAll">
      <text class="action-text">清空</text>
    </view>
    <view class="nav-placeholder" wx:else></view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <view class="color-list" wx:if="{{colorList.length > 0}}">
      <view class="color-item" wx:for="{{colorList}}" wx:key="timestamp" bindtap="selectColor" data-color="{{item}}">
        <view class="color-preview" style="background-color: {{item.rgb}};"></view>
        <view class="color-details">
          <text class="rgb-text">R {{item.r}} G {{item.g}} B {{item.b}}</text>
          <text class="hex-text">{{item.hex}}</text>
          <text class="save-time">{{item.timeText}}</text>
        </view>
        <view class="color-actions">
          <view class="action-btn" bindtap="copyColor" data-hex="{{item.hex}}" catchtap="true">
            <text class="action-icon">📋</text>
          </view>
          <view class="action-btn" bindtap="deleteColor" data-index="{{index}}" catchtap="true">
            <text class="action-icon">🗑️</text>
          </view>
        </view>
      </view>
    </view>

    <view class="empty-state" wx:else>
      <view class="empty-content">
        <text class="empty-icon">🎨</text>
        <text class="empty-title">暂无保存的颜色</text>
        <text class="empty-subtitle">去拍照分析颜色并保存吧</text>
        <button class="start-btn" bindtap="goToIndex">开始使用</button>
      </view>
    </view>
  </view>
</view>
