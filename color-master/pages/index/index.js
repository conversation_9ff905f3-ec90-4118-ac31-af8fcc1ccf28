// pages/index/index.js
Page({
  data: {
    selectedImage: '',
    selectedColor: null,
    colorSelected: false,
    tapX: 0,
    tapY: 0,
    // 像素放大镜相关
    showPixelMagnifier: false,
    pixelMagnifierX: 0,
    pixelMagnifierY: 0,
    pixelMagnifierData: null,
    // 实时颜色选择相关
    isSelecting: false,
    currentColor: null,
    // 图片信息
    imageInfo: null,
    // 显示用的颜色数据
    displayColor: null
  },

  onLoad() {
    console.log('首页加载完成')
  },

  onShow() {
    console.log('首页显示')
  },

  // 选择图片
  chooseImage() {
    const that = this
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        const tempFilePath = res.tempFilePaths[0]
        that.setData({
          selectedImage: tempFilePath,
          selectedColor: null,
          colorSelected: false,
          isSelecting: false,
          showPixelMagnifier: false,
          currentColor: null,
          displayColor: null
        })

        // 重置canvas状态
        that.canvasReady = false
        console.log('图片选择成功:', tempFilePath)
      },
      fail(err) {
        console.error('图片选择失败:', err)
        wx.showToast({
          title: '图片选择失败',
          icon: 'none'
        })
      }
    })
  },

  // 点击图片进入颜色选择模式
  onImageTap(e) {
    if (!this.data.selectedImage) return

    console.log('进入颜色选择模式')

    // 获取图片位置信息
    this.getImageInfo(() => {
      // 进入选择模式，不显示弹框
      this.setData({
        colorSelected: false,
        isSelecting: true,
        showPixelMagnifier: false // 初始不显示像素放大镜
      })

      wx.showToast({
        title: '滑动选择颜色',
        icon: 'none',
        duration: 1500
      })
    })
  },

  // 获取图片位置信息
  getImageInfo(callback) {
    wx.createSelectorQuery()
      .select('.selected-image')
      .boundingClientRect((rect) => {
        if (rect) {
          this.setData({
            imageInfo: rect
          })
          console.log('图片信息:', rect)
          callback && callback()
        } else {
          console.error('无法获取图片信息')
          wx.showToast({
            title: '无法获取图片信息',
            icon: 'none'
          })
        }
      })
      .exec()
  },

  // 图片触摸开始
  onImageTouchStart(e) {
    if (!this.data.isSelecting) return

    const touch = e.touches[0]
    this.updateColorSelection(touch.x, touch.y)
  },

  // 图片触摸移动
  onImageTouchMove(e) {
    if (!this.data.isSelecting) return

    const touch = e.touches[0]
    this.updateColorSelection(touch.x, touch.y)
  },

  // 图片触摸结束
  onImageTouchEnd(e) {
    if (!this.data.isSelecting) return

    // 确认选择的颜色
    if (this.data.currentColor) {
      this.setData({
        selectedColor: this.data.currentColor,
        displayColor: this.data.currentColor,
        colorSelected: true,
        isSelecting: false,
        showPixelMagnifier: false
      })
    }
  },

  // 更新颜色选择
  updateColorSelection(x, y) {
    const { imageInfo } = this.data
    if (!imageInfo) return

    // 获取原图的位置信息
    wx.createSelectorQuery()
      .select('.selected-image')
      .boundingClientRect((rect) => {
        if (!rect) return

        // 计算相对于原图的坐标
        const relativeX = x - rect.left
        const relativeY = y - rect.top

        // 确保坐标在图片范围内
        if (relativeX < 0 || relativeY < 0 || relativeX > rect.width || relativeY > rect.height) {
          return
        }

        // 映射到原图坐标比例
        const xRatio = relativeX / rect.width
        const yRatio = relativeY / rect.height

        // 计算原图中的实际像素坐标
        const originalX = xRatio * imageInfo.width
        const originalY = yRatio * imageInfo.height

        // 显示像素放大镜
        this.setData({
          showPixelMagnifier: true,
          pixelMagnifierX: x, // 屏幕坐标
          pixelMagnifierY: y
        })

        // 实时提取颜色和周围像素
        this.extractColorAndPixels(originalX, originalY)
      })
      .exec()
  },

  // 提取颜色和周围像素
  extractColorAndPixels(x, y) {
    // 防抖处理，避免过于频繁的调用
    if (this.colorExtractionTimer) {
      clearTimeout(this.colorExtractionTimer)
    }

    this.colorExtractionTimer = setTimeout(() => {
      this.doExtractColorAndPixels(x, y)
    }, 30) // 30ms防抖，更快响应
  },

  // 执行颜色和像素提取
  doExtractColorAndPixels(x, y) {
    const that = this
    const canvasSize = 300

    // 如果canvas还没有准备好，先准备canvas
    if (!this.canvasReady) {
      this.prepareCanvas(() => {
        this.doExtractColorAndPixels(x, y)
      })
      return
    }

    // 计算在canvas中的坐标位置
    const canvasX = Math.floor((x / this.data.imageInfo.width) * canvasSize)
    const canvasY = Math.floor((y / this.data.imageInfo.height) * canvasSize)

    // 确保坐标在canvas范围内
    const finalX = Math.max(0, Math.min(canvasX, canvasSize - 1))
    const finalY = Math.max(0, Math.min(canvasY, canvasSize - 1))

    // 提取中心点颜色
    wx.canvasGetImageData({
      canvasId: 'hiddenCanvas',
      x: finalX,
      y: finalY,
      width: 1,
      height: 1,
      success(res) {
        const data = res.data
        const r = data[0]
        const g = data[1]
        const b = data[2]

        // 计算三原色比例
        const total = r + g + b
        const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
        const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
        const bPercent = total > 0 ? Math.round((b / total) * 100) : 0

        const colorData = {
          r, g, b,
          rPercent, gPercent, bPercent
        }

        // 实时更新当前颜色
        that.setData({
          currentColor: colorData,
          displayColor: colorData
        })

        // 提取周围像素用于放大镜显示
        that.extractSurroundingPixels(finalX, finalY)
      },
      fail(err) {
        console.error('实时颜色提取失败:', err)
        // 使用备用方案
        that.simulateColorExtractionRealtime(x, y)
      }
    }, this)
  },

  // 提取周围像素
  extractSurroundingPixels(centerX, centerY) {
    const that = this
    const magnifierSize = 9 // 9x9像素网格
    const halfSize = Math.floor(magnifierSize / 2)

    // 计算提取区域
    const startX = Math.max(0, centerX - halfSize)
    const startY = Math.max(0, centerY - halfSize)
    const endX = Math.min(299, centerX + halfSize) // canvas是300x300
    const endY = Math.min(299, centerY + halfSize)

    const actualWidth = endX - startX + 1
    const actualHeight = endY - startY + 1

    wx.canvasGetImageData({
      canvasId: 'hiddenCanvas',
      x: startX,
      y: startY,
      width: actualWidth,
      height: actualHeight,
      success(res) {
        const pixelData = []
        const data = res.data

        // 转换像素数据为颜色数组
        for (let i = 0; i < data.length; i += 4) {
          pixelData.push({
            r: data[i],
            g: data[i + 1],
            b: data[i + 2]
          })
        }

        that.setData({
          pixelMagnifierData: {
            pixels: pixelData,
            width: actualWidth,
            height: actualHeight,
            centerX: centerX - startX,
            centerY: centerY - startY
          }
        })
      },
      fail(err) {
        console.error('提取周围像素失败:', err)
      }
    }, this)
  },

  // 准备Canvas
  prepareCanvas(callback) {
    const ctx = wx.createCanvasContext('hiddenCanvas', this)
    ctx.drawImage(this.data.selectedImage, 0, 0, 300, 300)
    ctx.draw(false, () => {
      this.canvasReady = true
      callback && callback()
    })
  },

  // 实时颜色提取备用方案
  simulateColorExtractionRealtime(x, y) {
    const r = Math.floor(Math.abs(x % 255))
    const g = Math.floor(Math.abs(y % 255))
    const b = Math.floor(Math.abs((x + y) % 255))

    const total = r + g + b
    const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
    const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
    const bPercent = total > 0 ? Math.round((b / total) * 100) : 0

    const colorData = {
      r, g, b,
      rPercent, gPercent, bPercent
    }

    this.setData({
      currentColor: colorData,
      displayColor: colorData
    })
  },

  // 退出颜色选择模式
  exitColorSelection() {
    // 清理定时器
    if (this.colorExtractionTimer) {
      clearTimeout(this.colorExtractionTimer)
      this.colorExtractionTimer = null
    }

    // 重置canvas状态
    this.canvasReady = false

    this.setData({
      isSelecting: false,
      showPixelMagnifier: false,
      currentColor: null,
      pixelMagnifierX: 0,
      pixelMagnifierY: 0,
      pixelMagnifierData: null,
      displayColor: this.data.selectedColor // 保持显示已选择的颜色
    })
  },

  // 从图片获取颜色
  getColorFromImage(x, y) {
    const that = this

    // 获取图片元素的位置和尺寸信息
    wx.createSelectorQuery()
      .select('.selected-image')
      .boundingClientRect(function(rect) {
        if (!rect) {
          console.error('无法获取图片位置信息')
          that.simulateColorExtraction(x, y)
          return
        }

        console.log('图片位置信息:', rect)

        // 计算点击位置相对于图片的坐标
        const relativeX = x - rect.left
        const relativeY = y - rect.top

        // 确保点击位置在图片范围内
        if (relativeX < 0 || relativeY < 0 || relativeX > rect.width || relativeY > rect.height) {
          console.log('点击位置超出图片范围')
          that.simulateColorExtraction(x, y)
          return
        }

        // 使用canvas获取像素数据
        that.extractColorFromCanvas(relativeX, relativeY, rect.width, rect.height)
      })
      .exec()
  },

  // 使用canvas提取颜色
  extractColorFromCanvas(x, y, imageWidth, imageHeight) {
    const that = this
    const canvasSize = 300 // canvas固定尺寸

    // 创建canvas上下文
    const ctx = wx.createCanvasContext('hiddenCanvas', this)

    // 绘制图片到canvas
    ctx.drawImage(this.data.selectedImage, 0, 0, canvasSize, canvasSize)
    ctx.draw(false, () => {
      // 计算在canvas中的坐标位置
      const canvasX = Math.floor((x / imageWidth) * canvasSize)
      const canvasY = Math.floor((y / imageHeight) * canvasSize)

      console.log('Canvas坐标:', canvasX, canvasY)

      // 确保坐标在canvas范围内
      const finalX = Math.max(0, Math.min(canvasX, canvasSize - 1))
      const finalY = Math.max(0, Math.min(canvasY, canvasSize - 1))

      // 获取像素数据
      wx.canvasGetImageData({
        canvasId: 'hiddenCanvas',
        x: finalX,
        y: finalY,
        width: 1,
        height: 1,
        success(res) {
          const data = res.data
          const r = data[0]
          const g = data[1]
          const b = data[2]

          console.log('提取的RGB值:', r, g, b)

          // 计算三原色比例
          const total = r + g + b
          const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
          const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
          const bPercent = total > 0 ? Math.round((b / total) * 100) : 0

          that.setData({
            selectedColor: {
              r, g, b,
              rPercent, gPercent, bPercent
            }
          })

          wx.showToast({
            title: '颜色提取成功',
            icon: 'success'
          })

          console.log('提取的颜色:', { r, g, b, rPercent, gPercent, bPercent })
        },
        fail(err) {
          console.error('获取像素数据失败:', err)
          // 备用方案：使用模拟数据
          that.simulateColorExtraction(x, y)
        }
      }, this)
    })
  },

  // 备用颜色提取方案（模拟）
  simulateColorExtraction(x, y) {
    console.log('使用模拟颜色提取方案')

    // 基于点击位置生成模拟颜色
    const r = Math.floor(Math.abs(x % 255))
    const g = Math.floor(Math.abs(y % 255))
    const b = Math.floor(Math.abs((x + y) % 255))

    const total = r + g + b
    const rPercent = total > 0 ? Math.round((r / total) * 100) : 0
    const gPercent = total > 0 ? Math.round((g / total) * 100) : 0
    const bPercent = total > 0 ? Math.round((b / total) * 100) : 0

    this.setData({
      selectedColor: {
        r, g, b,
        rPercent, gPercent, bPercent
      }
    })

    wx.showToast({
      title: '颜色提取完成（模拟）',
      icon: 'success'
    })

    console.log('模拟提取的颜色:', { r, g, b, rPercent, gPercent, bPercent })
  },

  // 跳转到颜色调整页面
  goToColorAnalysis() {
    if (!this.data.selectedColor) return

    wx.navigateTo({
      url: `/pages/colorAnalysis/colorAnalysis?r=${this.data.selectedColor.r}&g=${this.data.selectedColor.g}&b=${this.data.selectedColor.b}`
    })
  },

  // 跳转到颜色历史页面
  goToColorHistory() {
    wx.navigateTo({
      url: '/pages/colorHistory/colorHistory'
    })
  }
})
