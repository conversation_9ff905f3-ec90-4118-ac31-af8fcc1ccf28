<!--index.wxml-->
<view class="page">
  <!-- 顶部导航 -->
  <view class="nav-bar">
    <text class="nav-title">颜色大师</text>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 图片选择区域 -->
    <view class="image-section">
      <view class="image-container" wx:if="{{selectedImage}}" bindtap="onImageTap">
        <image src="{{selectedImage}}" mode="aspectFit" class="selected-image"
               bindtouchstart="onImageTouchStart"
               bindtouchmove="onImageTouchMove"
               bindtouchend="onImageTouchEnd"></image>
        <view class="tap-overlay" wx:if="{{!colorSelected && !isSelecting}}">
          <view class="tap-hint">
            <text class="hint-icon">👆</text>
            <text class="hint-text">轻触图片选择颜色</text>
          </view>
        </view>
        <view class="color-point" wx:if="{{colorSelected}}" style="left: {{tapX}}px; top: {{tapY}}px;"></view>

        <!-- 像素放大镜 -->
        <view class="pixel-magnifier" wx:if="{{showPixelMagnifier && pixelMagnifierData}}"
              style="left: {{pixelMagnifierX - 60}}px; top: {{pixelMagnifierY - 60}}px;">
          <view class="pixel-grid">
            <view class="pixel-row" wx:for="{{pixelMagnifierData.height}}" wx:for-index="row" wx:key="row">
              <view class="pixel-cell"
                    wx:for="{{pixelMagnifierData.width}}"
                    wx:for-index="col"
                    wx:key="col"
                    style="background-color: rgb({{pixelMagnifierData.pixels[row * pixelMagnifierData.width + col].r}}, {{pixelMagnifierData.pixels[row * pixelMagnifierData.width + col].g}}, {{pixelMagnifierData.pixels[row * pixelMagnifierData.width + col].b}});"
                    class="{{row === pixelMagnifierData.centerY && col === pixelMagnifierData.centerX ? 'center-pixel' : ''}}">
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="empty-image" wx:else>
        <view class="empty-content">
          <text class="empty-icon">📷</text>
          <text class="empty-text">选择图片开始分析</text>
          <text class="empty-desc">支持拍照或从相册选择</text>
        </view>
      </view>
    </view>

    <!-- 颜色信息卡片 -->
    <view class="color-card" wx:if="{{displayColor}}">
      <view class="color-preview" style="background-color: rgb({{displayColor.r}}, {{displayColor.g}}, {{displayColor.b}});"></view>
      <view class="color-info">
        <text class="rgb-text">R {{displayColor.r}} G {{displayColor.g}} B {{displayColor.b}}</text>
        <view class="percentage-info">
          <view class="percentage-item">
            <text class="percentage-label">红</text>
            <text class="percentage-value">{{displayColor.rPercent}}%</text>
          </view>
          <view class="percentage-item">
            <text class="percentage-label">绿</text>
            <text class="percentage-value">{{displayColor.gPercent}}%</text>
          </view>
          <view class="percentage-item">
            <text class="percentage-label">蓝</text>
            <text class="percentage-value">{{displayColor.bPercent}}%</text>
          </view>
        </view>
      </view>
      <view class="color-status" wx:if="{{isSelecting}}">
        <text class="status-text">正在选择颜色...</text>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <button class="action-btn primary" bindtap="chooseImage">
      <text class="btn-icon">{{selectedImage ? '🔄' : '📷'}}</text>
      <text class="btn-text">{{selectedImage ? '重新选择' : '选择图片'}}</text>
    </button>

    <button class="action-btn secondary" wx:if="{{selectedColor}}" bindtap="goToColorAnalysis">
      <text class="btn-icon">🎨</text>
      <text class="btn-text">调整颜色</text>
    </button>

    <button class="action-btn tertiary" bindtap="goToColorHistory">
      <text class="btn-icon">📋</text>
      <text class="btn-text">历史记录</text>
    </button>
  </view>



  <!-- 隐藏的canvas用于颜色提取 -->
  <canvas canvas-id="hiddenCanvas" class="hidden-canvas"></canvas>
</view>
