/* pages/index/index.wxss */
.page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.nav-bar {
  background: white;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  display: block;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 40rpx;
}

/* 图片选择区域 */
.image-section {
  margin-bottom: 40rpx;
}

.image-container {
  position: relative;
  width: 100%;
  height: 500rpx;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.selected-image {
  width: 100%;
  height: 100%;
}

.tap-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* 允许点击事件穿透 */
}

.tap-hint {
  text-align: center;
  color: white;
}

.hint-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 16rpx;
}

.hint-text {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
}

.color-point {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 4rpx solid white;
  border-radius: 50%;
  box-shadow: 0 0 16rpx rgba(0, 0, 0, 0.3);
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

.empty-image {
  width: 100%;
  height: 500rpx;
  background: white;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-icon {
  display: block;
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-desc {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 颜色信息卡片 */
.color-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.color-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.color-preview {
  width: 100%;
  height: 120rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 2rpx solid transparent;
}

.color-preview:active {
  transform: scale(0.98);
}

.color-info {
  text-align: center;
}

.rgb-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  display: block;
  letter-spacing: 2rpx;
}

.percentage-info {
  display: flex;
  justify-content: space-around;
}

.percentage-item {
  text-align: center;
}

.percentage-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.percentage-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

/* 底部操作区域 */
.bottom-actions {
  padding: 40rpx;
  background: white;
  border-top: 1rpx solid #eee;
}

.action-btn {
  width: 100%;
  height: 96rpx;
  background: white;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-size: 32rpx;
  color: #495057;
  transition: all 0.3s ease;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.action-btn.primary {
  background: #007aff;
  border-color: #007aff;
  color: white;
}

.action-btn.secondary {
  background: #34c759;
  border-color: #34c759;
  color: white;
}

.action-btn.tertiary {
  background: #8e8e93;
  border-color: #8e8e93;
  color: white;
}

.action-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}

/* 像素放大镜样式 */
.pixel-magnifier {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  background: white;
  border: 4rpx solid #007aff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  z-index: 100;
  pointer-events: none;
  overflow: hidden;
}

.pixel-grid {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pixel-row {
  flex: 1;
  display: flex;
  flex-direction: row;
}

.pixel-cell {
  flex: 1;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-sizing: border-box;
}

.center-pixel {
  border: 2rpx solid #007aff !important;
  box-shadow: inset 0 0 8rpx rgba(0, 122, 255, 0.5);
  position: relative;
}

.center-pixel::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6rpx;
  height: 6rpx;
  background: #007aff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 4rpx rgba(0, 122, 255, 0.8);
}

/* 颜色状态提示 */
.color-status {
  text-align: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #eee;
  margin-top: 24rpx;
}

.status-text {
  font-size: 28rpx;
  color: #007aff;
  animation: status-blink 2s infinite;
}

@keyframes status-blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

.hidden-canvas {
  position: fixed;
  top: -1000rpx;
  left: -1000rpx;
  width: 300rpx;
  height: 300rpx;
}
