/* pages/colorAnalysis/colorAnalysis.wxss */
.page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.nav-bar {
  background: white;
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 48rpx;
  color: #007aff;
  font-weight: 300;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.nav-placeholder {
  width: 60rpx;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 40rpx;
}

/* 颜色预览区域 */
.color-preview-section {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.color-display {
  width: 100%;
  height: 160rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.rgb-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: 4rpx;
}

/* 滑块控制区域 */
.sliders-section {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.slider-group {
  margin-bottom: 80rpx;
}

.slider-group:last-child {
  margin-bottom: 0;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.slider-label {
  font-size: 36rpx;
  font-weight: 500;
  color: #1a1a1a;
}

.slider-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  min-width: 80rpx;
  text-align: right;
}

.slider-container {
  position: relative;
  height: 48rpx;
}

.slider-track {
  position: absolute;
  top: 12rpx;
  left: 0;
  right: 0;
  height: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.red-track {
  background: linear-gradient(to right, #f8f9fa 0%, #ff4757 100%);
}

.green-track {
  background: linear-gradient(to right, #f8f9fa 0%, #2ed573 100%);
}

.blue-track {
  background: linear-gradient(to right, #f8f9fa 0%, #3742fa 100%);
}

.slider-fill {
  height: 100%;
  border-radius: 12rpx;
  transition: width 0.2s ease;
}

.red-fill {
  background: #ff4757;
}

.green-fill {
  background: #2ed573;
}

.blue-fill {
  background: #3742fa;
}

.custom-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 48rpx;
}

/* 底部操作区域 */
.bottom-actions {
  padding: 40rpx;
  background: white;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 96rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #007aff;
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #8e8e93;
  border: 2rpx solid #e9ecef;
}

.action-btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
}

.hidden-canvas {
  position: fixed;
  top: -1000rpx;
  left: -1000rpx;
  width: 300rpx;
  height: 300rpx;
}
