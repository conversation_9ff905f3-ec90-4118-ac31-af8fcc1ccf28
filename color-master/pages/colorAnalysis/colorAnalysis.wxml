<!--pages/colorAnalysis/colorAnalysis.wxml-->
<view class="page">
  <!-- 顶部导航 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="goBack">
      <text class="back-icon">‹</text>
    </view>
    <text class="nav-title">选择颜色</text>
    <view class="nav-placeholder"></view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 颜色预览区域 -->
    <view class="color-preview-section">
      <view class="color-display" style="background-color: rgb({{adjustedColor.r}}, {{adjustedColor.g}}, {{adjustedColor.b}});"></view>
      <text class="rgb-text">R {{adjustedColor.r}} G {{adjustedColor.g}} B {{adjustedColor.b}}</text>
    </view>

    <!-- RGB滑块控制区域 -->
    <view class="sliders-section">
      <!-- 红色滑块 -->
      <view class="slider-group">
        <view class="slider-header">
          <text class="slider-label">Red</text>
          <text class="slider-value">{{adjustedColor.r}}</text>
        </view>
        <view class="slider-container">
          <view class="slider-track red-track">
            <view class="slider-fill red-fill" style="width: {{(adjustedColor.r / 255) * 100}}%;"></view>
          </view>
          <slider
            class="custom-slider"
            min="0"
            max="255"
            value="{{adjustedColor.r}}"
            bindchange="onRedChange"
            bindchanging="onRedChanging"
            activeColor="transparent"
            backgroundColor="transparent"
            block-color="#ffffff"
            block-size="24"
          />
        </view>
      </view>

      <!-- 绿色滑块 -->
      <view class="slider-group">
        <view class="slider-header">
          <text class="slider-label">Green</text>
          <text class="slider-value">{{adjustedColor.g}}</text>
        </view>
        <view class="slider-container">
          <view class="slider-track green-track">
            <view class="slider-fill green-fill" style="width: {{(adjustedColor.g / 255) * 100}}%;"></view>
          </view>
          <slider
            class="custom-slider"
            min="0"
            max="255"
            value="{{adjustedColor.g}}"
            bindchange="onGreenChange"
            bindchanging="onGreenChanging"
            activeColor="transparent"
            backgroundColor="transparent"
            block-color="#ffffff"
            block-size="24"
          />
        </view>
      </view>

      <!-- 蓝色滑块 -->
      <view class="slider-group">
        <view class="slider-header">
          <text class="slider-label">Blue</text>
          <text class="slider-value">{{adjustedColor.b}}</text>
        </view>
        <view class="slider-container">
          <view class="slider-track blue-track">
            <view class="slider-fill blue-fill" style="width: {{(adjustedColor.b / 255) * 100}}%;"></view>
          </view>
          <slider
            class="custom-slider"
            min="0"
            max="255"
            value="{{adjustedColor.b}}"
            bindchange="onBlueChange"
            bindchanging="onBlueChanging"
            activeColor="transparent"
            backgroundColor="transparent"
            block-color="#ffffff"
            block-size="24"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <button class="action-btn secondary" bindtap="resetColor">
      <text class="btn-text">重置</text>
    </button>
    <button class="action-btn primary" bindtap="saveColor">
      <text class="btn-text">保存颜色</text>
    </button>
  </view>

  <!-- 隐藏的canvas用于颜色处理 -->
  <canvas canvas-id="hiddenCanvas" class="hidden-canvas"></canvas>
</view>
